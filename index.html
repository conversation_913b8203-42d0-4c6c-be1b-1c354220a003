<!DOCTYPE html>
<html lang="en" class="no-js">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Forest Truong's Resume</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta
      property="og:title"
      itemprop="name"
      content="Forest Truong's Resume"
    />
    <meta property="og:type" content="https://rungtruong.github.io/" />
    <meta property="og:url" content="rungtruong.github.io" />
    <meta
      property="og:image"
      itemprop="image"
      content="https://rungtruong.github.io/img/main_photo.jpg"
    />
    <meta
      property="og:description"
      itemprop="description"
      content="Senior Front End Developer"
    />
    <meta property="og:site_name" content="Forest Truong" />

    <meta name="description" content="Senior Front End Developer" />
    <meta
      name="keywords"
      content="truong<PERSON><PERSON><PERSON>,rungt<PERSON><PERSON>, rung truong,r<PERSON><PERSON> tr<PERSON>, tru<PERSON> dai hai rung, tr<PERSON><PERSON><PERSON> đại hai r<PERSON>ng, forest truong, forest trương, foresttruong"
    />
    <meta name="author" content="foresttruong" />

    <meta
      name="google-site-verification"
      content="r3XaZMekF_LV7HF5r_kqPdSG4CSzzBgU25Wwvbomfmg"
    />

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <link rel="stylesheet" href="css/reset.css" type="text/css" />
    <link rel="stylesheet" href="css/bootstrap-grid.min.css" type="text/css" />
    <link rel="stylesheet" href="css/animations.css" type="text/css" />
    <link rel="stylesheet" href="css/perfect-scrollbar.css" type="text/css" />
    <link rel="stylesheet" href="css/owl.carousel.css" type="text/css" />
    <link rel="stylesheet" href="css/magnific-popup.css" type="text/css" />
    <link rel="stylesheet" href="css/main.css" type="text/css" />

    <!-- Google tag (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-SDKZNH6707"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-SDKZNH6707");
    </script>
  </head>

  <body>
    <!-- Animated Background -->
    <div
      class="animated-bg"
      style="background-image: url(img/main_bg.png)"
    ></div>
    <!-- /Animated Background -->

    <!-- Loading animation -->
    <div class="preloader">
      <div class="preloader-animation">
        <div class="preloader-spinner"></div>
      </div>
    </div>
    <!-- /Loading animation -->

    <div class="page">
      <div class="page-content">
        <header id="site_header" class="header mobile-menu-hide">
          <div class="header-content">
            <div class="header-photo">
              <img src="img/main_photo.jpg" alt="Alex Smith" />
            </div>
            <div class="header-titles">
              <h2>Forest Truong</h2>
              <h4>Senior Front End Developer</h4>
              <!-- <h4>thenewbadger.crypto</h4> -->
            </div>
          </div>

          <ul class="main-menu">
            <li class="active">
              <a href="#home" class="nav-anim">
                <span class="menu-icon lnr lnr-home"></span>
                <span class="link-text">Home</span>
              </a>
            </li>
            <li>
              <a href="#about-me" class="nav-anim">
                <span class="menu-icon lnr lnr-user"></span>
                <span class="link-text">About Me</span>
              </a>
            </li>
            <li>
              <a href="#resume" class="nav-anim">
                <span class="menu-icon lnr lnr-graduation-hat"></span>
                <span class="link-text">Resume</span>
              </a>
            </li>
            <li>
              <a href="#portfolio" class="nav-anim">
                <span class="menu-icon lnr lnr-briefcase"></span>
                <span class="link-text">Portfolio</span>
              </a>
            </li>
            <li>
              <a href="#contact" class="nav-anim">
                <span class="menu-icon lnr lnr-envelope"></span>
                <span class="link-text">Contact</span>
              </a>
            </li>
          </ul>

          <div class="social-links">
            <ul>
              <li>
                <a
                  href="https://www.linkedin.com/in/forest-tr%C6%B0%C6%A1ng-462823177/"
                  target="_blank"
                  ><i class="fab fa-linkedin-in"></i
                ></a>
              </li>
              <li>
                <a href="https://github.com/rungtruong" target="_blank"
                  ><i class="fab fa-github-square"></i
                ></a>
              </li>
              <li>
                <a href="https://twitter.com/truong_forest" target="_blank"
                  ><i class="fab fa-twitter"></i
                ></a>
              </li>
            </ul>
          </div>

          <div class="header-buttons">
            <a
              href="./cv/RungTruong_FrontEnd.pdf"
              target="_blank"
              class="btn btn-primary"
              >Download CV</a
            >
          </div>

          <div class="copyrights">
            © 2018 by
            <a href="#about" class="text-primary">Forest Truong</a>
          </div>
        </header>

        <!-- Mobile Navigation -->
        <div class="menu-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <!-- End Mobile Navigation -->

        <!-- Arrows Nav -->
        <div class="arrows-nav">
          <div class="arrow-right">
            <i class="lnr lnr-chevron-right"></i>
          </div>
          <div class="arrow-left">
            <i class="lnr lnr-chevron-left"></i>
          </div>
        </div>
        <!-- End Arrows Nav -->

        <div class="content-area">
          <div class="animated-sections">
            <!-- Home Subpage -->
            <section data-id="home" class="animated-section start-page">
              <div class="section-content vcentered home-bg">
                <div class="row">
                  <div class="col-sm-12 col-md-12 col-lg-12">
                    <div class="title-block">
                      <h2>Forest Truong</h2>
                      <div class="owl-carousel text-rotation">
                        <div class="item">
                          <div class="sp-subtitle">
                            Senior Front End Developer
                          </div>
                          <br />
                          <div class="sp-subtitle">
                            "As a highly skilled front-end developer, I
                            specialize in creating exceptional web designs and
                            delivering superior user experiences."
                          </div>
                        </div>

                        <div class="item">
                          <div class="sp-subtitle">
                            Specializing in Angular and ReactJs development with
                            the philosophy of delivering quality results.
                          </div>
                          <br />
                          <div class="sp-subtitle">
                            My personal philosophy is "Do it with passion, or not at all"
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
            <!-- End of Home Subpage -->

            <!-- About Me Subpage -->
            <section data-id="about-me" class="animated-section">
              <div class="section-content">
                <div class="page-title">
                  <h2>About <span>Me</span></h2>
                </div>

                <!-- Personal Information -->
                <div class="row">
                  <div class="col-xs-12 col-sm-7">
                    <p>
                      As a highly experienced front-end developer and a
                      dedicated programming professional, I initiated my
                      programming career in the embedded industry before
                      transitioning to front-end development in 2019. I have
                      accumulated extensive hands-on experience in various
                      projects, including IoT, e-Commerce, e-Learning, and CMS.
                      I hold the title of a senior front-end developer and am
                      committed to continuously enhancing my skills and
                      expertise in this field.
                    </p>
                  </div>

                  <div class="col-xs-12 col-sm-5">
                    <div class="info-list">
                      <ul>
                        <li>
                          <span class="title">Age</span>
                          <span class="value">29</span>
                        </li>

                        <li>
                          <span class="title">Residence</span>
                          <span class="value">HCM City, Viet Nam</span>
                        </li>

                        <li>
                          <span class="title">Hobbies</span>
                          <span class="value"
                            >Play football & Strategy Games</span
                          >
                        </li>

                        <li>
                          <span class="title">Favorite Food</span>
                          <span class="value">Bread, Sandwich, Pizza </span>
                        </li>

                        <li>
                          <span class="title">Fav Beer ^_^</span>
                          <span class="value">Budweiser</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <!-- End of Personal Information -->

                <div class="white-space-50"></div>

                <!-- Services -->
                <div class="row">
                  <div class="col-xs-12 col-sm-12">
                    <div class="block-title">
                      <h3>What <span>I Do</span></h3>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-xs-12 col-sm-6">
                    <div class="col-inner">
                      <div class="info-list-w-icon">
                        <div class="info-block-w-icon">
                          <div class="ci-icon">
                            <i class="lnr lnr-rocket"></i>
                          </div>
                          <div class="ci-text">
                            <h4>Developer</h4>
                            <p>
                              As a seasoned front-end developer, I am
                              responsible for the development of new features,
                              maintenance, and optimization of existing web
                              features, and ensuring that the application
                              remains technologically up-to-date through
                              compatibility with the latest technology
                              advancements.
                            </p>
                          </div>
                        </div>
                        <div class="info-block-w-icon">
                          <div class="ci-icon">
                            <i class="lnr lnr-laptop-phone"></i>
                          </div>
                          <div class="ci-text">
                            <h4>Tech Lead Project</h4>
                            <p>
                              With my experience in managing teams and creating
                              projects from the ground up, I am equipped to
                              effectively document and estimate front-end tasks,
                              design comprehensive code structures and
                              architecture, and construct reusable code.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-xs-12 col-sm-6">
                    <div class="col-inner">
                      <div class="info-list-w-icon">
                        <div class="info-block-w-icon">
                          <div class="ci-icon">
                            <i class="lnr lnr-pencil"></i>
                          </div>
                          <div class="ci-text">
                            <h4>Coaching & Mentoring</h4>
                            <p>
                              I possess extensive experience in coaching and
                              consulting other developers. Additionally, I am
                              tasked with training team members and providing
                              mentorship to junior/intern developers, ensuring
                              their successful completion of delegated tasks.
                            </p>
                          </div>
                        </div>
                        <div class="info-block-w-icon">
                          <div class="ci-icon">
                            <i class="lnr lnr-flag"></i>
                          </div>
                          <div class="ci-text">
                            <h4>Growth Driver</h4>
                            <p>
                              I am driven by a continuous pursuit of exploring
                              and incorporating new and emerging technologies
                              into my work. My extensive project experience has
                              allowed me to develop a high level of proficiency
                              in technologies such as Angular, ReactJs, and
                              NestJs,...
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- End of Services -->

                <div class="white-space-30"></div>

                <div class="row">
                  <div class="col-xs-12 col-sm-12">
                    <div class="block-title">
                      <h3>Testimonials</h3>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-xs-12 col-sm-12">
                    <div class="testimonials owl-carousel">
                      <div class="testimonial">
                        <div class="img">
                          <img src="img/testimonials/thomas.jpg" alt="Thomas" />
                        </div>
                        <div class="text">
                          <p>
                            I have worked with Forest. He is very diligent and
                            meticulous. Also excellent in all aspects. Super
                            compliant. more than recommended.
                          </p>
                        </div>

                        <div class="author-info">
                          <h4 class="author">Thomas Vo</h4>
                          <h5 class="company">STYL Solutions Pte Ltd - HCMC</h5>
                          <div class="icon">
                            <i class="fas fa-quote-right"></i>
                          </div>
                        </div>
                      </div>

                      <div class="testimonial">
                        <div class="img">
                          <img src="img/testimonials/jacky.jpg" alt="Jacky" />
                        </div>
                        <div class="text">
                          <p>
                            I have worked with Forest on some projects and in
                            the cases, the work was done on time, on budget, and
                            with a high degree of professionalism.
                          </p>
                        </div>

                        <div class="author-info">
                          <h4 class="author">Jacky</h4>
                          <h5 class="company">STYL Solutions Pte Ltd - HCMC</h5>
                          <div class="icon">
                            <i class="fas fa-quote-right"></i>
                          </div>
                        </div>
                      </div>

                      <div class="testimonial">
                        <div class="img">
                          <img src="img/testimonials/nguyet.jpg" alt="Nguyet" />
                        </div>
                        <div class="text">
                          <p>
                            It has been a real pleasure working with Forest. He
                            has been very professional and very clear in all
                            communications, which I appreciate.
                          </p>
                        </div>

                        <div class="author-info">
                          <h4 class="author">Nguyet</h4>
                          <h5 class="company">STYL Solutions Pte Ltd - HCMC</h5>
                          <div class="icon">
                            <i class="fas fa-quote-right"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="white-space-50"></div>

                <!-- Fun Facts
                  <div class="row">
                    <div class="col-xs-12 col-sm-12">

                      <div class="block-title">
                        <h3>Fun <span>Facts</span></h3>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-xs-12 col-sm-4">
                      <div class="fun-fact gray-default">
                        <i class="lnr lnr-heart"></i>
                        <h4>Happy Clients</h4>
                        <span class="fun-fact-block-value">578</span>
                        <span class="fun-fact-block-text"></span>
                      </div>
                    </div>

                    <div class="col-xs-12 col-sm-4">
                      <div class="fun-fact gray-default">
                        <i class="lnr lnr-clock"></i>
                        <h4>Working Hours</h4>
                        <span class="fun-fact-block-value">2,000</span>
                        <span class="fun-fact-block-text"></span>
                      </div>
                    </div>

                    <div class="col-xs-12 col-sm-4 ">
                      <div class="fun-fact gray-default">
                        <i class="lnr lnr-star"></i>
                        <h4>Awards Won</h4>
                        <span class="fun-fact-block-value">15</span>
                        <span class="fun-fact-block-text"></span>
                      </div>
                    </div>
                  </div>
                  End of Fun Facts -->
              </div>
            </section>
            <!-- End of About Me Subpage -->

            <!-- Resume Subpage -->
            <section data-id="resume" class="animated-section">
              <div class="section-content">
                <div class="page-title">
                  <h2>Resume</h2>
                </div>

                <div class="row">
                  <div class="col-xs-12 col-sm-7">
                    <div class="block-title">
                      <h3>Experience</h3>
                    </div>

                    <div class="timeline timeline-second-style clearfix">
                      <div class="timeline-item clearfix">
                        <div class="left-part">
                          <h5 class="item-period">2019</h5>
                          <span class="item-company"
                            >STYL Solutions Pte Ltd - HCMC</span
                          >
                        </div>
                        <div class="divider"></div>
                        <div class="right-part">
                          <h4 class="item-title">
                            Senior Front End Software Engineer
                          </h4>
                          <p>
                            As a seasoned front-end developer, I specialize in
                            the design and development of web applications
                            utilizing ReactJS and Angular frameworks. My
                            expertise encompasses the analysis, design,
                            programming, testing (both UI and functional),
                            documentation, and maintenance of web applications.
                            Additionally, I provide support to junior developers
                            to resolve issues and train interns to enhance their
                            skills and knowledge.
                          </p>
                        </div>
                      </div>
                      <div class="timeline-item clearfix">
                        <div class="left-part">
                          <h5 class="item-period">2016 - 2018</h5>
                          <span class="item-company">Kantar Media VN</span>
                        </div>
                        <div class="divider"></div>
                        <div class="right-part">
                          <h4 class="item-title">Embedded Engineer</h4>
                          <p>
                            With extensive experience in electronics
                            engineering, I specialize in the design of printed
                            circuit boards and embedded programming for matching
                            and detection applications.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div class="white-space-50"></div>

                    <div class="block-title">
                      <h3>Education</h3>
                    </div>

                    <div class="timeline timeline-second-style clearfix">
                      <div class="timeline-item clearfix">
                        <div class="left-part">
                          <h5 class="item-period">2018</h5>
                          <span class="item-company">CyberSoft Academy</span>
                        </div>
                        <div class="divider"></div>
                        <div class="right-part">
                          <h4 class="item-title">
                            Front End Programming Expert
                          </h4>
                          <p>
                            The Front End Programming Expert course is a
                            comprehensive training program, specializing in the
                            latest technologies, including Angular and ReactJS.
                            The course is designed to provide participants with
                            a deep and thorough understanding of these
                            technologies, enabling them to excel in the field
                            and meet the demands of the industry.
                          </p>
                        </div>
                      </div>

                      <div class="timeline-item clearfix">
                        <div class="left-part">
                          <h5 class="item-period">2012</h5>
                          <span class="item-company">IUH</span>
                        </div>
                        <div class="divider"></div>
                        <div class="right-part">
                          <h4 class="item-title">
                            Electronics and Telecommunications Engineer
                          </h4>
                          <p>
                            The Electronics and Telecommunications Engineer
                            course is a specialized training program that offers
                            a comprehensive education in the field of
                            electronics and telecommunications. Participants
                            will receive training in networking,
                            telecommunication networks, routing switches,
                            microprocessor programming, embedded programming,
                            and automation, among other essential skills. Upon
                            completion, participants will have acquired the
                            advanced knowledge and technical competencies
                            required to excel in the industry.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Skills & Certificates -->
                  <div class="col-xs-12 col-sm-5">
                    <!-- Coding Skills -->
                    <div class="block-title">
                      <h3>Coding <span>Skills</span></h3>
                    </div>

                    <div class="skills-info skills-second-style">
                      <!-- Skill 5 -->
                      <div class="skill clearfix">
                        <h4>Angular</h4>
                        <div class="skill-value">80%</div>
                      </div>
                      <div class="skill-container skill-8">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 5 -->

                      <!-- Skill 6 -->
                      <div class="skill clearfix">
                        <h4>ReactJs/Hook/Redux/Thunk</h4>
                        <div class="skill-value">70%</div>
                      </div>
                      <div class="skill-container skill-7">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 6 -->

                      <!-- Skill 7 -->
                      <div class="skill clearfix">
                        <h4>HTML/CSS/SASS</h4>
                        <div class="skill-value">90%</div>
                      </div>
                      <div class="skill-container skill-9">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 7 -->

                      <!-- Skill 8 -->
                      <div class="skill clearfix">
                        <h4>JavaScript/JQuery/TypeScript</h4>
                        <div class="skill-value">80%</div>
                      </div>
                      <div class="skill-container skill-8">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 8 -->

                      <!-- Skill 9 -->
                      <div class="skill clearfix">
                        <h4>Bootstrap/Material/Ant Design/Tailwind CSS</h4>
                        <div class="skill-value">90%</div>
                      </div>
                      <div class="skill-container skill-9">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 9 -->
                    </div>
                    <!-- End of Coding Skills -->

                    <div class="white-space-10"></div>

                    <!-- Design Skills -->
                    <div class="block-title">
                      <h3>Business <span>Skills</span></h3>
                    </div>

                    <div class="skills-info skills-second-style">
                      <!-- Skill 1 -->
                      <div class="skill clearfix">
                        <h4>Team Work</h4>
                        <div class="skill-value">80%</div>
                      </div>
                      <div class="skill-container skill-8">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 1 -->

                      <!-- Skill 2 -->
                      <div class="skill clearfix">
                        <h4>Communication</h4>
                        <div class="skill-value">95%</div>
                      </div>
                      <div class="skill-container skill-95">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 2 -->

                      <!-- Skill 3 -->
                      <div class="skill clearfix">
                        <h4>Project Management</h4>
                        <div class="skill-value">90%</div>
                      </div>
                      <div class="skill-container skill-9">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 3 -->

                      <!-- Skill 4 -->
                      <div class="skill clearfix">
                        <h4>Critical Thinking</h4>
                        <div class="skill-value">80%</div>
                      </div>
                      <div class="skill-container skill-8">
                        <div class="skill-percentage"></div>
                      </div>
                      <!-- End of Skill 4 -->
                    </div>
                    <!-- End of Design Skills -->

                    <div class="white-space-10"></div>

                    <!-- Knowledges -->
                    <div class="block-title">
                      <h3>Knowledges</h3>
                    </div>

                    <ul class="knowledges">
                      <li>Communication</li>
                      <li>Critical Thinking</li>
                      <li>Angular</li>
                      <li>ReactJs</li>
                      <li>Typescript</li>
                      <li>Git</li>
                      <li>Jira</li>
                      <li>Redmind</li>
                      <li>Zeplin</li>
                      <li>Figma</li>
                      <li>Photoshop</li>
                      <li>Adobe Illustrator</li>
                      <li>Project Management</li>
                      <li>Problem-Solving</li>
                      <li>Self Motivated</li>
                      <li>NodeJS</li>
                      <li>NestJS</li>
                      <li>MongoDB</li>
                      <li>MySQL</li>
                      <li>Webpack</li>
                    </ul>
                    <!-- End of Knowledges -->
                  </div>
                  <!-- End of Skills & Certificates -->
                </div>

                <div class="white-space-50"></div>

                <!-- Certificates -->
                <div class="row">
                  <div class="col-xs-12 col-sm-12">
                    <div class="block-title">
                      <h3>Certificates</h3>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <!-- Certificate 1 -->
                  <div class="col-xs-12 col-sm-6">
                    <div class="certificate-item clearfix">
                      <div class="certi-logo">
                        <img src="img/clients/client-1.jpg" alt="logo" />
                      </div>

                      <div class="certi-content">
                        <div class="certi-title">
                          <h4>Professional Front End Developer</h4>
                        </div>
                        <div class="certi-id">
                          <span>Certificate No: FE/2019/000125</span>
                        </div>
                        <div class="certi-date">
                          <span>Apr 2019</span>
                        </div>
                        <div class="certi-company">
                          <span></span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- End of Certificate 1 -->

                  <!-- Certificate 2 -->
                  <div class="col-xs-12 col-sm-6">
                    <div class="certificate-item clearfix">
                      <div class="certi-logo">
                        <img src="img/clients/client-2.jpg" alt="logo" />
                      </div>

                      <div class="certi-content">
                        <div class="certi-title">
                          <h4>Electronics and Telecommunications Engineer</h4>
                        </div>
                        <div class="certi-id">
                          <span></span>
                        </div>
                        <div class="certi-date">
                          <span>Dec 2016</span>
                        </div>
                        <div class="certi-company">
                          <span></span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- End of Certificate 2 -->
                </div>
                <!-- End of Certificates -->
              </div>
            </section>
            <!-- End of Resume Subpage -->

            <!-- Portfolio Subpage -->
            <section data-id="portfolio" class="animated-section">
              <div class="section-content">
                <div class="page-title">
                  <h2>Portfolio</h2>
                </div>

                <div class="row">
                  <div class="col-xs-12 col-sm-12">
                    <!-- Portfolio Content -->
                    <div class="portfolio-content">
                      <ul class="portfolio-filters">
                        <li class="active">
                          <a
                            class="filter btn btn-sm btn-link"
                            data-group="category_all"
                            >All</a
                          >
                        </li>
                        <li>
                          <a
                            class="filter btn btn-sm btn-link"
                            data-group="category_angular"
                            >Angular</a
                          >
                        </li>
                        <li>
                          <a
                            class="filter btn btn-sm btn-link"
                            data-group="category_react"
                            >ReacJs</a
                          >
                        </li>
                        <li>
                          <a
                            class="filter btn btn-sm btn-link"
                            data-group="category_vanilla"
                            >VanillaJS</a
                          >
                        </li>
                      </ul>

                      <!-- Portfolio Grid -->
                      <div class="portfolio-grid three-columns">
                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/indoor.png"
                              alt="Indoor Farm"
                              title=""
                            />
                            <a
                              href="src/portfolio-indoor.html"
                              class="ajax-page-load"
                              title="Indoor Farm"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">Indoor Farm</h4>
                          <span class="category">Detailed</span>
                        </figure>

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/digital-learning.png"
                              alt="Digital Learning"
                              title=""
                            />
                            <a
                              href="src/portfolio-digital.html"
                              class="ajax-page-load"
                              title="Digital Learning"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">Digital Learning</h4>
                          <span class="category">Detailed</span>
                        </figure>

                        <!-- <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/fb.png"
                              alt="Farm Box"
                              title=""
                            />
                            <a
                              href="src/portfolio-farmbox.html"
                              class="ajax-page-load"
                              title="Farm Box"
                            ></a>
                          </div>

                           <i class="far fa-file-alt"></i>
                          <h4 class="name">Farm Box</h4>
                          <span class="category">Detailed</span>
                        </figure> -->

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_react"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/npark.png"
                              alt="Npark"
                              title=""
                            />
                            <a
                              href="src/portfolio-npark.html"
                              class="ajax-page-load"
                              title="Npark"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">Npark</h4>
                          <span class="category">Detailed</span>
                        </figure>

                        <!-- <figure
                          class="item standard"
                          data-groups='["category_all", "category_react"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/fas.png"
                              alt="FAS"
                              title=""
                            />
                            <a
                              href="src/portfolio-1.html"
                              class="ajax-page-load"
                              title="FAS"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">FAS</h4>
                          <span class="category">Detailed</span>
                        </figure> -->

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_vanilla"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/eden-smartfarm.png"
                              alt="eden-smartfarm"
                              title=""
                            />
                            <a
                              href="https://eden-smartfarm.com"
                              target="_blank"
                              title="eden-smartfarm"
                            ></a>
                          </div>

                          <i class="fa fa-link"></i>
                          <h4 class="name">Eden Smart Farm</h4>
                          <span class="category">Linked</span>
                        </figure>

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/drone.png"
                              alt="Drone"
                              title=""
                            />
                            <a
                              href="src/portfolio-drone.html"
                              class="ajax-page-load"
                              title="Drone"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">Drone</h4>
                          <span class="category">Detailed</span>
                        </figure>

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_vanilla"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/styl-landing.png"
                              alt="styl"
                              title=""
                            />
                            <a
                              href="https://styl.com.sg"
                              target="_blank"
                              title="styl"
                            ></a>
                          </div>

                          <i class="fa fa-link"></i>
                          <h4 class="name">STYL Landing Page</h4>
                          <span class="category">Linked</span>
                        </figure>

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/iSchool.jpg"
                              alt="iSchool"
                              title=""
                            />
                            <a
                              href="src/portfolio-iSchool.html"
                              class="ajax-page-load"
                              title="iSchool"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">iSchool</h4>
                          <span class="category">Detailed</span>
                        </figure>

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/sb.png"
                              alt="Smart Buddy"
                              title=""
                            />
                            <a
                              href="src/portfolio-sb.html"
                              class="ajax-page-load"
                              title="Smart Buddy"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">Smart Buddy</h4>
                          <span class="category">Detailed</span>
                        </figure>

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_react"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/edsh.png"
                              alt="EDSH"
                              title=""
                            />
                            <a
                              href="src/portfolio-edsh.html"
                              class="ajax-page-load"
                              title="EDSH"
                            ></a>
                          </div>

                          <!-- <i class="far fa-file-alt"></i> -->
                          <h4 class="name">EDSH</h4>
                          <span class="category">Not Detail</span>
                        </figure>

                        <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/sodexo.png"
                              alt="Sodexo"
                              title=""
                            />
                            <a
                              href="src/portfolio-sodexo.html"
                              class="ajax-page-load"
                              title="Sodexo"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">Sodexo</h4>
                          <span class="category">Detailed</span>
                        </figure>

                        <!-- <figure
                          class="item standard"
                          data-groups='["category_all", "category_angular"]'
                        >
                          <div class="portfolio-item-img">
                            <img
                              src="img/portfolio/indoor.png"
                              alt="Delphinus"
                              title=""
                            />
                            <a
                              href="src/portfolio-1.html"
                              class="ajax-page-load"
                              title="Delphinus"
                            ></a>
                          </div>

                          <i class="far fa-file-alt"></i>
                          <h4 class="name">Delphinus</h4>
                          <span class="category">Detailed</span>
                        </figure> -->
                      </div>
                    </div>
                    <!-- End of Portfolio Content -->
                  </div>
                </div>
              </div>
            </section>
            <!-- End of Portfolio Subpage -->

            <!-- Contact Subpage -->
            <section data-id="contact" class="animated-section">
              <div class="section-content">
                <div class="page-title">
                  <h2>Contact</h2>
                </div>

                <div class="row">
                  <!-- Contact Info -->
                  <div class="col-xs-12 col-sm-4">
                    <div class="info-block gray-default">
                      <i class="lnr lnr-map-marker"></i>
                      <h4>Le Duc Tho, Go Vap, HCM City, VN</h4>
                      <span class="info-block-value"></span>
                      <span class="info-block-text"></span>
                    </div>

                    <div class="info-block gray-default">
                      <i class="lnr lnr-envelope"></i>
                      <h4><EMAIL></h4>
                      <span class="info-block-value"></span>
                      <span class="info-block-text"></span>
                    </div>

                    <div class="info-block gray-default">
                      <i class="lnr lnr-checkmark-circle"></i>
                      <h4>Freelance Available</h4>
                      <span class="info-block-value"></span>
                      <span class="info-block-text"></span>
                    </div>
                  </div>
                  <!-- End of Contact Info -->

                  <!-- Contact Form -->
                  <div class="col-xs-12 col-sm-8">
                    <div class="map">
                      <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3918.579409639442!2d106.67150146430332!3d10.843463736149639!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x317529ad3b4d036b%3A0xb81c76e173ab9e01!2zNDcyIEzDqiDEkOG7qWMgVGjhu40sIFBoxrDhu51uZyAxNywgR8OyIFbhuqVwLCBUaMOgbmggcGjhu5EgSOG7kyBDaMOtIE1pbmgsIFZp4buHdCBOYW0!5e0!3m2!1svi!2sus!4v1675235547290!5m2!1svi!2sus"
                        width="100%"
                        height="140px"
                        style="border: 0"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"
                      ></iframe>
                    </div>
                    <div class="block-title">
                      <h3>How Can I <span>Help You?</span></h3>
                    </div>

                    <form
                      id="contact-form"
                      class="contact-form"
                      action="https://formspree.io/f/mbjedpgy"
                      method="POST"
                    >
                      <div class="messages"></div>

                      <div class="controls two-columns">
                        <div class="fields clearfix">
                          <div class="left-column">
                            <div class="form-group form-group-with-icon">
                              <input
                                id="form_name"
                                type="text"
                                name="name"
                                class="form-control"
                                placeholder=""
                                required="required"
                                data-error="Name is required."
                              />
                              <label>Full Name</label>
                              <div class="form-control-border"></div>
                              <div class="help-block with-errors"></div>
                            </div>

                            <div class="form-group form-group-with-icon">
                              <input
                                id="form_email"
                                type="email"
                                name="email"
                                class="form-control"
                                placeholder=""
                                required="required"
                                data-error="Valid email is required."
                              />
                              <label>Email Address</label>
                              <div class="form-control-border"></div>
                              <div class="help-block with-errors"></div>
                            </div>

                            <div class="form-group form-group-with-icon">
                              <input
                                id="form_subject"
                                type="text"
                                name="subject"
                                class="form-control"
                                placeholder=""
                                required="required"
                                data-error="Subject is required."
                              />
                              <label>Subject</label>
                              <div class="form-control-border"></div>
                              <div class="help-block with-errors"></div>
                            </div>
                          </div>
                          <div class="right-column">
                            <div class="form-group form-group-with-icon">
                              <textarea
                                id="form_message"
                                name="message"
                                class="form-control"
                                placeholder=""
                                rows="7"
                                required="required"
                                data-error="Please, leave me a message."
                              ></textarea>
                              <label>Message</label>
                              <div class="form-control-border"></div>
                              <div class="help-block with-errors"></div>
                            </div>
                          </div>
                        </div>

                        <input
                          type="submit"
                          class="button btn-send"
                          value="Send message"
                        />

                        <div id="contact-form-status"></div>
                      </div>
                    </form>
                  </div>
                  <!-- End of Contact Form -->
                </div>
              </div>
            </section>
            <!-- End of Contact Subpage -->
          </div>
        </div>
      </div>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/modernizr.custom.js"></script>
    <script src="js/animating.js"></script>
    <script src="js/imagesloaded.pkgd.min.js"></script>
    <script src="js/perfect-scrollbar.min.js"></script>
    <script src="js/jquery.shuffle.min.js"></script>
    <script src="js/masonry.pkgd.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>

    <script src="js/validator.js"></script>
    <script src="js/main.js"></script>
    <script src="js/sender.js"></script>
  </body>
</html>
