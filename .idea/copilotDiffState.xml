<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotDiffPersistence">
    <option name="pendingDiffs">
      <map>
        <entry key="$PROJECT_DIR$/.github/workflows/deploy.yml">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/.github/workflows/deploy.yml" />
              <option name="originalContent" value="name: Release foresttruong.info&#10;on: [push]&#10;jobs:&#10;  build:&#10;    runs-on: ubuntu-latest&#10;    environment: foresttruong.info&#10;    steps:&#10;      - uses: actions/checkout@v1&#10;      - name: Copy repository contents via scp&#10;        uses: appleboy/scp-action@master&#10;        with:&#10;          host: ${{ secrets.HOST }}&#10;          username: ${{ secrets.USERNAME }}&#10;          port: ${{ secrets.PORT }}&#10;          key: ${{ secrets.SSHKEY }}&#10;          source: &quot;.&quot;&#10;          target: &quot;/opt/portfolio&quot;&#10;      - name: Executing remote command&#10;        uses: appleboy/ssh-action@master&#10;        with:&#10;          host: ${{ secrets.HOST }}&#10;          username: ${{ secrets.USERNAME }}&#10;          port: ${{ secrets.PORT }}&#10;          key: ${{ secrets.SSHKEY }}&#10;          script: |&#10;            cd /opt/portfolio&#10;            if ! command -v npx &gt; /dev/null; then&#10;              curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -&#10;              sudo apt-get install -y nodejs&#10;            fi&#10;            npx serve -s . -l 4647 &amp;&#10;" />
              <option name="updatedContent" value="name: Release foresttruong.info&#10;on: [push]&#10;jobs:&#10;  build:&#10;    runs-on: ubuntu-latest&#10;    environment: foresttruong.info&#10;    steps:&#10;      - uses: actions/checkout@v1&#10;      - name: Copy repository contents via scp&#10;        uses: appleboy/scp-action@master&#10;        with:&#10;          host: ${{ secrets.HOST }}&#10;          username: ${{ secrets.USERNAME }}&#10;          port: ${{ secrets.PORT }}&#10;          key: ${{ secrets.SSHKEY }}&#10;          source: &quot;.&quot;&#10;          target: &quot;/opt/portfolio&quot;&#10;      - name: Build and run Docker container&#10;        uses: appleboy/ssh-action@master&#10;        with:&#10;          host: ${{ secrets.HOST }}&#10;          username: ${{ secrets.USERNAME }}&#10;          port: ${{ secrets.PORT }}&#10;          key: ${{ secrets.SSHKEY }}&#10;          script: |&#10;            cd /opt/portfolio&#10;            docker compose pull || true&#10;            docker compose build --pull&#10;            docker compose up -d --remove-orphans" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/Dockerfile">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/Dockerfile" />
              <option name="updatedContent" value="# Use official Node.js image&#10;FROM node:18-alpine&#10;&#10;# Set working directory&#10;WORKDIR /opt/portfolio&#10;&#10;# Copy static files&#10;COPY . .&#10;&#10;# Install serve&#10;RUN npm install -g serve&#10;&#10;# Expose port&#10;EXPOSE 4647&#10;&#10;# Start static server&#10;CMD [&quot;serve&quot;, &quot;-s&quot;, &quot;.&quot;, &quot;-l&quot;, &quot;4647&quot;]&#10;" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/docker-compose.yml">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/docker-compose.yml" />
              <option name="updatedContent" value="version: '3.8'&#10;services:&#10;  portfolio-static:&#10;    build: .&#10;    container_name: portfolio-static&#10;    ports:&#10;      - &quot;4647:4647&quot;&#10;    restart: always&#10;    volumes:&#10;      - ./:/opt/portfolio&#10;" />
            </PendingDiffInfo>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>