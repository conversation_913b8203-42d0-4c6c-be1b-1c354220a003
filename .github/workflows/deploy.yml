name: Release foresttruong.info
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    environment: foresttruong.info
    steps:
      - uses: actions/checkout@v1
      - name: Copy repository contents via scp
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          port: ${{ secrets.PORT }}
          key: ${{ secrets.SSHKEY }}
          source: "."
          target: "/opt/portfolio"
      - name: Build and run Docker container
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          port: ${{ secrets.PORT }}
          key: ${{ secrets.SSHKEY }}
          script: |
            cd /opt/portfolio
            docker compose pull || true
            docker compose build --pull
            docker compose up -d --remove-orphans
