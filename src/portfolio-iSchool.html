<div id="ajax-page" class="ajax-page-content">
  <div class="ajax-page-wrapper">
    <div class="ajax-page-nav">
      <div class="nav-item ajax-page-prev-next">
        <a class="ajax-page-load" href="src/portfolio-npark.html"
          ><i class="lnr lnr-chevron-left"></i
        ></a>
        <a class="ajax-page-load" href="src/portfolio-sb.html"
          ><i class="lnr lnr-chevron-right"></i
        ></a>
      </div>
      <div class="nav-item ajax-page-close-button">
        <a id="ajax-page-close-button" href="#"
          ><i class="lnr lnr-cross"></i
        ></a>
      </div>
    </div>

    <div class="ajax-page-title">
      <h1>iSchool</h1>
    </div>

    <div class="row">
      <div class="col-sm-8 col-md-8 portfolio-block">
        <div class="owl-carousel portfolio-page-carousel">
          <div class="item">
            <img src="../img/portfolio/iSchool.jpg" alt="" />
          </div>
          <div class="item">
            <img src="../img/portfolio/iSchool-1.png" alt="" />
          </div>
          <div class="item">
            <img src="../img/portfolio/iSchool-2.png" alt="" />
          </div>
          <div class="item">
            <img src="../img/portfolio/iSchool-3.png" alt="" />
          </div>
          <div class="item">
            <img src="../img/portfolio/iSchool-4.png" alt="" />
          </div>
        </div>
        <script type="text/javascript">
          jQuery(document).ready(function ($) {
            $(".portfolio-page-carousel").imagesLoaded(function () {
              $(".portfolio-page-carousel").owlCarousel({
                smartSpeed: 1200,
                items: 1,
                loop: true,
                dots: true,
                nav: true,
                navText: false,
                margin: 10,
                autoHeight: true,
              });
            });
          });
        </script>
      </div>

      <div class="col-sm-4 col-md-4 portfolio-block">
        <!-- Project Description -->
        <div class="project-description">
          <div class="block-title">
            <h3>Description</h3>
          </div>

          <p class="text-justify">
            A portal site for managing point-of-sale (POS) machines in schools,
            including features such as user management, access authorization,
            menu management, item management, order management, reporting, and
            kiosk management.
          </p>
          <p>Team Size: 9</p>
          <p>Position: Senior Front End Software Engineer</p>
          <!-- /Project Description -->

          <!-- Responsibilities -->
          <div class="tags-block">
            <div class="block-title">
              <h3>Responsibilities</h3>
            </div>
            <ul class="project-general-info">
              <li>
                <p><i class="fa fa-tasks"></i> Task management</p>
                <p><i class="fa fa-tasks"></i> Analysis and design</p>
                <p><i class="fa fa-tasks"></i> Development</p>
                <p><i class="fa fa-tasks"></i> Code review and optimization</p>
                <p><i class="fa fa-tasks"></i> Bug fixing</p>
              </li>
            </ul>
          </div>
          <!-- /Responsibilities -->

          <!-- Technology -->
          <div class="tags-block">
            <div class="block-title">
              <h3>Technologies Used</h3>
            </div>
            <ul class="tags">
              <li><a>HTML5</a></li>
              <li><a>CSS3</a></li>
              <li><a>Angular 8</a></li>
              <li><a>TypeScript</a></li>
              <li><a>SCSS</a></li>
              <li><a>Bootstrap</a></li>
              <li><a>ChartJs</a></li>
              <li><a>Summer Note</a></li>
              <li><a>Git</a></li>
              <li><a>Jira</a></li>
            </ul>
          </div>

          <!-- /Technology -->
        </div>
        <!-- Project Description -->
      </div>
    </div>
  </div>
</div>
