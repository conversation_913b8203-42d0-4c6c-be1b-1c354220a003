<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Truong's Resume - Next.js Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Poppins', sans-serif; }
        .bg-gradient-primary { background: linear-gradient(135deg, #fe6d72 0%, #f58545 100%); }
        .text-gradient { 
            background: linear-gradient(135deg, #fe6d72 0%, #f58545 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .animate-bounce-slow { animation: bounce 2s infinite; }
        .animate-pulse-slow { animation: pulse 3s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="fixed left-0 top-0 h-full w-80 bg-white shadow-lg z-50 hidden lg:block">
        <div class="flex flex-col h-full">
            <!-- Profile Section -->
            <div class="p-8 text-center border-b border-gray-200">
                <div class="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden bg-gradient-primary flex items-center justify-center">
                    <i class="fas fa-user text-white text-4xl"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-800 mb-1">Forest Truong</h2>
                <h4 class="text-sm text-gray-600">Senior Front End Developer</h4>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 py-4">
                <ul class="space-y-1">
                    <li><a href="#home" class="w-full flex items-center px-6 py-3 text-left bg-gradient-primary text-white"><i class="fas fa-home mr-3"></i>Home</a></li>
                    <li><a href="#about" class="w-full flex items-center px-6 py-3 text-left text-gray-700 hover:bg-gradient-primary hover:text-white"><i class="fas fa-user mr-3"></i>About Me</a></li>
                    <li><a href="#resume" class="w-full flex items-center px-6 py-3 text-left text-gray-700 hover:bg-gradient-primary hover:text-white"><i class="fas fa-graduation-cap mr-3"></i>Resume</a></li>
                    <li><a href="#portfolio" class="w-full flex items-center px-6 py-3 text-left text-gray-700 hover:bg-gradient-primary hover:text-white"><i class="fas fa-briefcase mr-3"></i>Portfolio</a></li>
                    <li><a href="#contact" class="w-full flex items-center px-6 py-3 text-left text-gray-700 hover:bg-gradient-primary hover:text-white"><i class="fas fa-envelope mr-3"></i>Contact</a></li>
                </ul>
            </nav>

            <!-- Social Links -->
            <div class="p-6 border-t border-gray-200">
                <div class="flex justify-center space-x-4 mb-4">
                    <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gradient-primary hover:text-white"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gradient-primary hover:text-white"><i class="fab fa-github"></i></a>
                    <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gradient-primary hover:text-white"><i class="fab fa-twitter"></i></a>
                </div>
                <button class="w-full bg-gradient-primary text-white px-6 py-3 rounded-lg font-medium">Download CV</button>
            </div>
        </div>
    </header>

    <!-- Mobile Header -->
    <header class="lg:hidden fixed top-0 left-0 right-0 bg-white shadow-md z-50">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-full bg-gradient-primary flex items-center justify-center">
                    <i class="fas fa-user text-white"></i>
                </div>
                <div>
                    <h2 class="text-sm font-semibold text-gray-800">Forest Truong</h2>
                    <p class="text-xs text-gray-600">Senior Front End Developer</p>
                </div>
            </div>
            <button class="w-8 h-8 flex flex-col justify-center items-center space-y-1">
                <span class="w-6 h-0.5 bg-gray-600"></span>
                <span class="w-6 h-0.5 bg-gray-600"></span>
                <span class="w-6 h-0.5 bg-gray-600"></span>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="lg:ml-80 pt-16 lg:pt-0">
        <!-- Hero Section -->
        <section id="home" class="min-h-screen bg-gradient-primary flex items-center justify-center relative overflow-hidden">
            <!-- Background Elements -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-5 rounded-full animate-bounce-slow"></div>
                <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-3 rounded-full animate-pulse-slow"></div>
            </div>
            
            <div class="relative z-10 text-center text-white px-4">
                <h1 class="text-4xl md:text-6xl font-bold mb-4">Forest Truong</h1>
                <h2 class="text-xl md:text-3xl font-semibold mb-6">Senior Front End Developer</h2>
                <p class="text-lg mb-8 max-w-2xl mx-auto opacity-90">
                    "As a highly skilled front-end developer, I specialize in creating exceptional web designs and delivering superior user experiences."
                </p>
                <p class="text-base opacity-75 italic">
                    My personal philosophy is "Do it with passion, or not at all"
                </p>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="py-16 px-4 sm:px-6 lg:px-8">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        About <span class="text-gradient">Me</span>
                    </h2>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
                    <div>
                        <p class="text-gray-700 leading-relaxed text-lg">
                            As a highly experienced front-end developer and a dedicated programming professional, I initiated my programming career in the embedded industry before transitioning to front-end development in 2019. I have accumulated extensive hands-on experience in various projects, including IoT, e-Commerce, e-Learning, and CMS.
                        </p>
                    </div>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center py-3 border-b border-gray-200">
                            <span class="font-medium text-gray-900">Age</span>
                            <span class="text-gray-600">29</span>
                        </div>
                        <div class="flex justify-between items-center py-3 border-b border-gray-200">
                            <span class="font-medium text-gray-900">Residence</span>
                            <span class="text-gray-600">HCM City, Viet Nam</span>
                        </div>
                        <div class="flex justify-between items-center py-3 border-b border-gray-200">
                            <span class="font-medium text-gray-900">Hobbies</span>
                            <span class="text-gray-600">Play football & Strategy Games</span>
                        </div>
                    </div>
                </div>

                <!-- Services -->
                <div class="text-center mb-12">
                    <h3 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                        What <span class="text-gradient">I Do</span>
                    </h3>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-xl shadow-md text-center group hover:shadow-lg transition-all duration-300">
                        <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-gradient-primary text-white group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-rocket text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Developer</h3>
                        <p class="text-gray-600 leading-relaxed">
                            As a seasoned front-end developer, I am responsible for the development of new features, maintenance, and optimization of existing web features.
                        </p>
                    </div>
                    
                    <div class="bg-white p-6 rounded-xl shadow-md text-center group hover:shadow-lg transition-all duration-300">
                        <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-gradient-primary text-white group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-laptop text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Tech Lead Project</h3>
                        <p class="text-gray-600 leading-relaxed">
                            With my experience in managing teams and creating projects from the ground up, I am equipped to effectively document and estimate front-end tasks.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Notice Section -->
        <section class="py-16 px-4 sm:px-6 lg:px-8 bg-blue-50">
            <div class="max-w-4xl mx-auto text-center">
                <div class="bg-white p-8 rounded-xl shadow-lg">
                    <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-info-circle text-blue-500 text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Next.js Application Ready!</h3>
                    <p class="text-gray-600 mb-6">
                        This is a preview of your new Next.js portfolio website. The complete application has been built with modern technologies including:
                    </p>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center">
                            <i class="fab fa-react text-3xl text-blue-500 mb-2"></i>
                            <p class="text-sm font-medium">Next.js 14</p>
                        </div>
                        <div class="text-center">
                            <i class="fab fa-js-square text-3xl text-yellow-500 mb-2"></i>
                            <p class="text-sm font-medium">TypeScript</p>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-paint-brush text-3xl text-purple-500 mb-2"></i>
                            <p class="text-sm font-medium">Tailwind CSS</p>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-magic text-3xl text-green-500 mb-2"></i>
                            <p class="text-sm font-medium">GSAP + Lenis</p>
                        </div>
                    </div>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <p class="text-yellow-800 text-sm">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <strong>Node.js Update Required:</strong> Please update Node.js to version 18+ to run the full Next.js application.
                        </p>
                    </div>
                    <div class="space-y-2 text-sm text-gray-600">
                        <p><strong>Current Node.js:</strong> v10.15.2</p>
                        <p><strong>Required:</strong> v18.0.0 or higher</p>
                        <p><strong>Recommended:</strong> v20.x.x (LTS)</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Simple smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Simple mobile menu toggle
        const mobileMenuButton = document.querySelector('header.lg\\:hidden button');
        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', function() {
                alert('Mobile menu functionality will be fully implemented in the Next.js version!');
            });
        }
    </script>
</body>
</html>
